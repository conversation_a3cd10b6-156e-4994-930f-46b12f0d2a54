// CONTOH PENGGUNAAN UNTUK MENGATASI ERROR 401
// 
// File ini berisi contoh-contoh cara menggunakan utility authentication
// yang telah dibuat untuk mengatasi masalah 401 Unauthorized

import 'package:smartfarming_bapeltan/api/admin_auth.dart';
import 'package:smartfarming_bapeltan/utils/auth_debug.dart';
import 'package:smartfarming_bapeltan/utils/api_helper.dart';
import 'package:smartfarming_bapeltan/utils/auth_troubleshoot.dart';

class AuthUsageExample {
  
  // CONTOH 1: Debug authentication ketika mendapat error 401
  static Future<void> debugWhen401() async {
    print('=== CONTOH DEBUG AUTHENTICATION ===');
    
    // Debug token yang tersimpan
    await AuthDebug.debugAuthToken();
    
    // Cek apakah user sudah login
    bool isAuth = await AuthDebug.isAuthenticated();
    print('User authenticated: $isAuth');
    
    // Cek apakah user adalah admin
    bool isAdmin = await AdminAuth.isAdmin();
    print('Is admin: $isAdmin');
  }
  
  // CONTOH 2: Login sebagai admin untuk mengakses endpoint admin
  static Future<void> loginAsAdmin() async {
    print('=== CONTOH LOGIN ADMIN ===');
    
    // Ganti dengan kredensial admin yang benar
    String adminEmail = '<EMAIL>';
    String adminPassword = 'admin_password';
    
    var result = await AdminAuth.adminLogin(adminEmail, adminPassword);
    
    if (result['success']) {
      print('Admin login berhasil!');
      print('Admin ID: ${result['admin']['id']}');
      print('Admin Email: ${result['admin']['email']}');
    } else {
      print('Admin login gagal: ${result['message']}');
    }
  }
  
  // CONTOH 3: Menggunakan ApiHelper untuk request yang aman
  static Future<void> safeApiRequest() async {
    print('=== CONTOH API REQUEST YANG AMAN ===');
    
    String url = 'http://34.101.210.210:8080/api/collections/users/records';
    
    // Request dengan automatic retry dan error handling
    var result = await ApiHelper.makeAuthenticatedRequest(
      url: url,
      method: 'GET',
      useAdminAuth: true, // Set true jika endpoint butuh admin auth
      maxRetries: 2,
    );
    
    if (result['success']) {
      print('API request berhasil!');
      print('Data: ${result['data']}');
    } else {
      print('API request gagal: ${result['message']}');
      
      if (result['needsReauth'] == true) {
        print('Perlu login ulang!');
        // Handle re-authentication
      }
    }
  }
  
  // CONTOH 4: Troubleshoot error 401 secara otomatis
  static Future<void> troubleshoot401Error(String failedUrl) async {
    print('=== CONTOH TROUBLESHOOTING 401 ===');
    
    // Diagnose masalah
    var diagnosis = await AuthTroubleshoot.diagnoseAuthIssue(failedUrl);
    
    print('Issues found: ${diagnosis['issues']}');
    print('Recommendations: ${diagnosis['recommendations']}');
    
    // Coba quick fix
    var quickFix = await AuthTroubleshoot.quickFix401(failedUrl);
    
    if (quickFix['success']) {
      print('Quick fix berhasil: ${quickFix['message']}');
    } else {
      print('Quick fix gagal: ${quickFix['message']}');
      print('Action required: ${quickFix['action']}');
      print('Instructions: ${quickFix['instructions']}');
    }
  }
  
  // CONTOH 5: Test semua authentication
  static Future<void> testAllAuthentication() async {
    print('=== CONTOH TEST SEMUA AUTHENTICATION ===');
    
    await AuthTroubleshoot.testAuthentication();
  }
  
  // CONTOH 6: Clear authentication data ketika logout
  static Future<void> clearAllAuthData() async {
    print('=== CONTOH CLEAR AUTH DATA ===');
    
    // Clear user auth data
    await AuthDebug.clearAuthData();
    
    // Clear admin auth data
    await AdminAuth.clearAdminAuth();
    
    print('Semua data authentication telah dihapus');
  }
  
  // CONTOH 7: Refresh token ketika expired
  static Future<void> refreshTokenExample() async {
    print('=== CONTOH REFRESH TOKEN ===');
    
    // Refresh admin token
    var result = await AdminAuth.refreshAdminToken();
    
    if (result['success']) {
      print('Token berhasil di-refresh');
    } else {
      print('Refresh token gagal: ${result['message']}');
      // Perlu login ulang
    }
  }
}

// PANDUAN PENGGUNAAN:
//
// 1. Ketika mendapat error 401, jalankan:
//    await AuthUsageExample.debugWhen401();
//
// 2. Jika endpoint memerlukan admin auth, login sebagai admin:
//    await AuthUsageExample.loginAsAdmin();
//
// 3. Untuk API request yang aman, gunakan:
//    await AuthUsageExample.safeApiRequest();
//
// 4. Untuk troubleshoot otomatis:
//    await AuthUsageExample.troubleshoot401Error('your_failed_url');
//
// 5. Untuk test semua authentication:
//    await AuthUsageExample.testAllAuthentication();

// ENDPOINT YANG MEMERLUKAN ADMIN AUTH:
// - /api/admins/*
// - /api/collections/users/records (untuk create user)
// - /api/logs/*
// - /api/settings/*
//
// ENDPOINT YANG MEMERLUKAN USER AUTH:
// - /api/collections/*/records (untuk CRUD data user)
// - /api/files/*
// - User-specific endpoints

// TROUBLESHOOTING CHECKLIST:
// ✓ Apakah PocketBase server berjalan?
// ✓ Apakah URL endpoint benar?
// ✓ Apakah sudah login sebagai user/admin?
// ✓ Apakah token masih valid (belum expired)?
// ✓ Apakah endpoint memerlukan admin privileges?
// ✓ Apakah network connectivity OK?
