import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartfarming_bapeltan/api/admin_auth.dart';
import 'package:smartfarming_bapeltan/utils/auth_debug.dart';

class ApiHelper {
  // Make authenticated API call with automatic retry on 401
  static Future<dynamic> makeAuthenticatedRequest({
    required String url,
    required String method,
    Map<String, dynamic>? body,
    bool useAdminAuth = false,
    int maxRetries = 1,
  }) async {
    
    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Get appropriate headers
        Map<String, String> headers = useAdminAuth 
            ? await AdminAuth.getAdminHeaders()
            : await AuthDebug.getAuthHeaders();
        
        print('API Request - Attempt ${attempt + 1}');
        print('URL: $url');
        print('Method: $method');
        print('Headers: $headers');
        if (body != null) print('Body: ${jsonEncode(body)}');
        
        http.Response response;
        
        switch (method.toUpperCase()) {
          case 'GET':
            response = await http.get(Uri.parse(url), headers: headers);
            break;
          case 'POST':
            response = await http.post(
              Uri.parse(url),
              headers: headers,
              body: body != null ? jsonEncode(body) : null,
            );
            break;
          case 'PATCH':
            response = await http.patch(
              Uri.parse(url),
              headers: headers,
              body: body != null ? jsonEncode(body) : null,
            );
            break;
          case 'DELETE':
            response = await http.delete(Uri.parse(url), headers: headers);
            break;
          default:
            throw Exception('Unsupported HTTP method: $method');
        }
        
        print('Response Status: ${response.statusCode}');
        print('Response Body: ${response.body}');
        
        // Handle successful response
        if (response.statusCode >= 200 && response.statusCode < 300) {
          var jsonResponse = json.decode(response.body);
          return {
            'success': true,
            'data': jsonResponse,
            'statusCode': response.statusCode,
          };
        }
        
        // Handle 401 Unauthorized
        if (response.statusCode == 401) {
          print('401 Unauthorized - Attempt ${attempt + 1}');
          
          var errorData = json.decode(response.body);
          print('401 Error Details: $errorData');
          
          // If this is not the last attempt, try to refresh token
          if (attempt < maxRetries) {
            print('Attempting to refresh token...');
            
            if (useAdminAuth) {
              var refreshResult = await AdminAuth.refreshAdminToken();
              if (refreshResult['success']) {
                print('Admin token refreshed, retrying...');
                continue; // Retry with new token
              } else {
                print('Admin token refresh failed: ${refreshResult['message']}');
              }
            } else {
              // For user tokens, we might need to re-login
              print('User token expired, need to re-login');
              await AuthDebug.debugAuthToken();
            }
          }
          
          return {
            'success': false,
            'message': errorData['message'] ?? 'Unauthorized access',
            'statusCode': 401,
            'needsReauth': true,
          };
        }
        
        // Handle other error responses
        var errorData = json.decode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'API request failed',
          'statusCode': response.statusCode,
          'data': errorData,
        };
        
      } catch (error) {
        print('API Request Error (Attempt ${attempt + 1}): $error');
        
        if (attempt == maxRetries) {
          return {
            'success': false,
            'message': 'Network error: $error',
            'statusCode': 0,
          };
        }
      }
    }
    
    return {
      'success': false,
      'message': 'Max retries exceeded',
      'statusCode': 0,
    };
  }
  
  // Check if endpoint requires admin authentication
  static bool requiresAdminAuth(String url) {
    // List of endpoints that require admin authentication
    List<String> adminEndpoints = [
      '/api/admins',
      '/api/collections/users/records', // Creating users
      '/api/logs',
      '/api/settings',
    ];
    
    return adminEndpoints.any((endpoint) => url.contains(endpoint));
  }
  
  // Handle authentication error
  static Future<void> handleAuthError(bool isAdmin) async {
    if (isAdmin) {
      await AdminAuth.clearAdminAuth();
      print('Admin authentication cleared due to error');
    } else {
      await AuthDebug.clearAuthData();
      print('User authentication cleared due to error');
    }
  }
  
  // Debug API permissions
  static Future<void> debugApiPermissions(String url) async {
    print('=== API PERMISSIONS DEBUG ===');
    print('URL: $url');
    print('Requires Admin Auth: ${requiresAdminAuth(url)}');
    
    await AuthDebug.debugAuthToken();
    
    bool isAdmin = await AdminAuth.isAdmin();
    String? adminToken = await AdminAuth.getAdminToken();
    
    print('Is Admin: $isAdmin');
    print('Admin Token: ${adminToken != null ? 'Present' : 'Missing'}');
    print('=== END PERMISSIONS DEBUG ===');
  }
}
