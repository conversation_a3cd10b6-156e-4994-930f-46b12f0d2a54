import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartfarming_bapeltan/common/url.dart';

class AdminAuth {
  // Admin login for PocketBase
  static Future<dynamic> adminLogin(String email, String password) async {
    String apiURL = '${UrlData().baseUrl}/api/admins/auth-with-password';
    
    Map<String, String> header = {
      'Content-type': 'application/json',
    };
    
    var body = jsonEncode({
      'identity': email,
      'password': password
    });
    
    try {
      print('Admin Login API URL: $apiURL');
      print('Admin Login Request Body: $body');
      
      var apiResult = await http.post(
        Uri.parse(apiURL),
        body: body,
        headers: header,
      );
      
      print('Admin Login Response Status: ${apiResult.statusCode}');
      print('Admin Login Response Body: ${apiResult.body}');
      
      var jsonObject = json.decode(apiResult.body);
      
      if (apiResult.statusCode == 200) {
        // Success - save admin token
        await saveAdminToken(jsonObject['token'], jsonObject['admin']);
        
        return {
          'success': true,
          'admin': jsonObject['admin'],
          'token': jsonObject['token'],
          'message': 'Admin login successful'
        };
      } else {
        return {
          'success': false,
          'message': jsonObject['message'] ?? 'Admin login failed',
          'data': jsonObject
        };
      }
    } catch (error) {
      print('Admin login network error: $error');
      return {'success': false, 'message': 'Network error: $error'};
    }
  }
  
  // Save admin token to SharedPreferences
  static Future<void> saveAdminToken(String token, Map<String, dynamic> adminData) async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString('adminToken', token);
      await pref.setString('adminId', adminData['id']);
      await pref.setString('adminEmail', adminData['email']);
      await pref.setBool('isAdmin', true);
      
      print('Admin token saved successfully');
    } catch (e) {
      print('Save admin token error: $e');
    }
  }
  
  // Get admin token from SharedPreferences
  static Future<String?> getAdminToken() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      return pref.getString('adminToken');
    } catch (e) {
      print('Get admin token error: $e');
      return null;
    }
  }
  
  // Check if user is admin
  static Future<bool> isAdmin() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      return pref.getBool('isAdmin') ?? false;
    } catch (e) {
      print('Check admin status error: $e');
      return false;
    }
  }
  
  // Get admin headers for API calls
  static Future<Map<String, String>> getAdminHeaders() async {
    Map<String, String> headers = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
    };
    
    try {
      String? adminToken = await getAdminToken();
      
      if (adminToken != null && adminToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $adminToken';
        print('Added admin auth header');
      } else {
        print('WARNING: No admin token found');
      }
    } catch (e) {
      print('Get admin headers error: $e');
    }
    
    return headers;
  }
  
  // Clear admin authentication data
  static Future<void> clearAdminAuth() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.remove('adminToken');
      await pref.remove('adminId');
      await pref.remove('adminEmail');
      await pref.remove('isAdmin');
      print('Admin auth data cleared');
    } catch (e) {
      print('Clear admin auth error: $e');
    }
  }
  
  // Refresh admin token
  static Future<dynamic> refreshAdminToken() async {
    try {
      String? currentToken = await getAdminToken();
      if (currentToken == null) {
        return {'success': false, 'message': 'No admin token to refresh'};
      }
      
      String apiURL = '${UrlData().baseUrl}/api/admins/auth-refresh';
      
      Map<String, String> headers = {
        'Content-type': 'application/json',
        'Authorization': 'Bearer $currentToken',
      };
      
      var apiResult = await http.post(Uri.parse(apiURL), headers: headers);
      var jsonObject = json.decode(apiResult.body);
      
      if (apiResult.statusCode == 200) {
        await saveAdminToken(jsonObject['token'], jsonObject['admin']);
        return {
          'success': true,
          'token': jsonObject['token'],
          'message': 'Admin token refreshed'
        };
      } else {
        return {
          'success': false,
          'message': jsonObject['message'] ?? 'Token refresh failed'
        };
      }
    } catch (error) {
      print('Admin token refresh error: $error');
      return {'success': false, 'message': 'Network error: $error'};
    }
  }
}
