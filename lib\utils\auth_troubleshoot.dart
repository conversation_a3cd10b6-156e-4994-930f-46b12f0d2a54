import 'package:smartfarming_bapeltan/api/admin_auth.dart';
import 'package:smartfarming_bapeltan/utils/auth_debug.dart';
import 'package:smartfarming_bapeltan/utils/api_helper.dart';
import 'package:smartfarming_bapeltan/common/url.dart';

class AuthTroubleshoot {
  // Comprehensive authentication troubleshooting
  static Future<Map<String, dynamic>> diagnoseAuthIssue(String failedUrl) async {
    print('=== AUTHENTICATION TROUBLESHOOTING ===');
    print('Failed URL: $failedUrl');
    
    Map<String, dynamic> diagnosis = {
      'url': failedUrl,
      'issues': <String>[],
      'recommendations': <String>[],
      'requiresAdmin': false,
      'hasUserAuth': false,
      'hasAdminAuth': false,
    };
    
    // Check if URL requires admin authentication
    bool requiresAdmin = ApiHelper.requiresAdminAuth(failedUrl);
    diagnosis['requiresAdmin'] = requiresAdmin;
    
    // Check user authentication
    bool hasUserAuth = await AuthDebug.isAuthenticated();
    diagnosis['hasUserAuth'] = hasUserAuth;
    
    // Check admin authentication
    bool hasAdminAuth = await AdminAuth.isAdmin();
    String? adminToken = await AdminAuth.getAdminToken();
    diagnosis['hasAdminAuth'] = hasAdminAuth && adminToken != null;
    
    // Analyze issues
    if (requiresAdmin && !diagnosis['hasAdminAuth']) {
      diagnosis['issues'].add('Endpoint requires admin authentication but no admin token found');
      diagnosis['recommendations'].add('Login as admin using AdminAuth.adminLogin()');
    }
    
    if (!requiresAdmin && !hasUserAuth) {
      diagnosis['issues'].add('User not authenticated');
      diagnosis['recommendations'].add('Login as user first');
    }
    
    if (requiresAdmin && hasUserAuth && !diagnosis['hasAdminAuth']) {
      diagnosis['issues'].add('Trying to access admin endpoint with user credentials');
      diagnosis['recommendations'].add('Use admin credentials for this endpoint');
    }
    
    // Print diagnosis
    print('Requires Admin: ${diagnosis['requiresAdmin']}');
    print('Has User Auth: ${diagnosis['hasUserAuth']}');
    print('Has Admin Auth: ${diagnosis['hasAdminAuth']}');
    
    if (diagnosis['issues'].isNotEmpty) {
      print('ISSUES FOUND:');
      for (String issue in diagnosis['issues']) {
        print('- $issue');
      }
      
      print('RECOMMENDATIONS:');
      for (String rec in diagnosis['recommendations']) {
        print('- $rec');
      }
    } else {
      print('No obvious authentication issues found');
      diagnosis['recommendations'].add('Check server logs for more details');
      diagnosis['recommendations'].add('Verify PocketBase server is running');
      diagnosis['recommendations'].add('Check network connectivity');
    }
    
    print('=== END TROUBLESHOOTING ===');
    return diagnosis;
  }
  
  // Test authentication with different methods
  static Future<void> testAuthentication() async {
    print('=== TESTING AUTHENTICATION ===');
    
    // Test basic connectivity
    await testConnectivity();
    
    // Test user authentication
    await testUserAuth();
    
    // Test admin authentication
    await testAdminAuth();
    
    print('=== END AUTHENTICATION TEST ===');
  }
  
  // Test basic connectivity to PocketBase
  static Future<void> testConnectivity() async {
    print('Testing basic connectivity...');
    
    try {
      String healthUrl = '${UrlData().baseUrl}/api/health';
      var result = await ApiHelper.makeAuthenticatedRequest(
        url: healthUrl,
        method: 'GET',
        useAdminAuth: false,
        maxRetries: 0,
      );
      
      if (result['success']) {
        print('✓ PocketBase server is reachable');
      } else {
        print('✗ PocketBase server connectivity issue: ${result['message']}');
      }
    } catch (e) {
      print('✗ Connectivity test failed: $e');
    }
  }
  
  // Test user authentication
  static Future<void> testUserAuth() async {
    print('Testing user authentication...');
    
    bool isAuth = await AuthDebug.isAuthenticated();
    if (isAuth) {
      print('✓ User authentication data found');
      
      // Test a user endpoint
      try {
        String testUrl = '${UrlData().url_users}/records';
        var result = await ApiHelper.makeAuthenticatedRequest(
          url: testUrl,
          method: 'GET',
          useAdminAuth: false,
        );
        
        if (result['success']) {
          print('✓ User authentication is working');
        } else {
          print('✗ User authentication failed: ${result['message']}');
        }
      } catch (e) {
        print('✗ User auth test error: $e');
      }
    } else {
      print('✗ No user authentication data found');
    }
  }
  
  // Test admin authentication
  static Future<void> testAdminAuth() async {
    print('Testing admin authentication...');
    
    bool isAdmin = await AdminAuth.isAdmin();
    String? adminToken = await AdminAuth.getAdminToken();
    
    if (isAdmin && adminToken != null) {
      print('✓ Admin authentication data found');
      
      // Test an admin endpoint
      try {
        String testUrl = '${UrlData().baseUrl}/api/admins/auth-refresh';
        var result = await ApiHelper.makeAuthenticatedRequest(
          url: testUrl,
          method: 'POST',
          useAdminAuth: true,
        );
        
        if (result['success']) {
          print('✓ Admin authentication is working');
        } else {
          print('✗ Admin authentication failed: ${result['message']}');
        }
      } catch (e) {
        print('✗ Admin auth test error: $e');
      }
    } else {
      print('✗ No admin authentication data found');
    }
  }
  
  // Quick fix for common 401 issues
  static Future<Map<String, dynamic>> quickFix401(String failedUrl) async {
    print('=== QUICK FIX FOR 401 ERROR ===');
    
    var diagnosis = await diagnoseAuthIssue(failedUrl);
    
    // Try automatic fixes
    if (diagnosis['requiresAdmin'] && !diagnosis['hasAdminAuth']) {
      return {
        'success': false,
        'message': 'Admin login required',
        'action': 'ADMIN_LOGIN_REQUIRED',
        'instructions': 'Use AdminAuth.adminLogin(email, password) to authenticate as admin'
      };
    }
    
    if (!diagnosis['hasUserAuth']) {
      return {
        'success': false,
        'message': 'User login required',
        'action': 'USER_LOGIN_REQUIRED',
        'instructions': 'Login as user first using the login screen'
      };
    }
    
    // Try token refresh if we have authentication
    if (diagnosis['hasAdminAuth']) {
      print('Attempting admin token refresh...');
      var refreshResult = await AdminAuth.refreshAdminToken();
      if (refreshResult['success']) {
        return {
          'success': true,
          'message': 'Admin token refreshed successfully',
          'action': 'TOKEN_REFRESHED'
        };
      }
    }
    
    return {
      'success': false,
      'message': 'Could not automatically fix 401 error',
      'action': 'MANUAL_INTERVENTION_REQUIRED',
      'diagnosis': diagnosis
    };
  }
}
