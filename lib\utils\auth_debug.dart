import 'package:shared_preferences/shared_preferences.dart';

class AuthDebug {
  // Debug authentication token
  static Future<void> debugAuthToken() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      
      String? authToken = pref.getString('authToken');
      String? userId = pref.getString('userId');
      String? username = pref.getString('username');
      String? email = pref.getString('email');
      
      print('=== AUTH DEBUG INFO ===');
      print('Auth Token: ${authToken ?? 'NULL'}');
      print('User ID: ${userId ?? 'NULL'}');
      print('Username: ${username ?? 'NULL'}');
      print('Email: ${email ?? 'NULL'}');
      
      if (authToken != null) {
        print('Token Length: ${authToken.length}');
        print('Token starts with: ${authToken.substring(0, authToken.length > 20 ? 20 : authToken.length)}...');
      }
      
      print('=== END AUTH DEBUG ===');
    } catch (e) {
      print('Auth Debug Error: $e');
    }
  }
  
  // Check if user is properly authenticated
  static Future<bool> isAuthenticated() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      String? authToken = pref.getString('authToken');
      String? userId = pref.getString('userId');
      
      return authToken != null && authToken.isNotEmpty && 
             userId != null && userId.isNotEmpty;
    } catch (e) {
      print('Authentication check error: $e');
      return false;
    }
  }
  
  // Clear all authentication data
  static Future<void> clearAuthData() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.remove('authToken');
      await pref.remove('userId');
      await pref.remove('username');
      await pref.remove('email');
      await pref.remove('telepon');
      await pref.remove('alamat');
      print('Auth data cleared successfully');
    } catch (e) {
      print('Clear auth data error: $e');
    }
  }
  
  // Get auth headers for API calls
  static Future<Map<String, String>> getAuthHeaders() async {
    Map<String, String> headers = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
    };
    
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      String? authToken = pref.getString('authToken');
      
      if (authToken != null && authToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $authToken';
        print('Added auth header with token: ${authToken.substring(0, authToken.length > 20 ? 20 : authToken.length)}...');
      } else {
        print('WARNING: No auth token found for API call');
      }
    } catch (e) {
      print('Get auth headers error: $e');
    }
    
    return headers;
  }
}
